import { useEffect, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { TokenManager, TeamManager } from '@/libs/storage'
import { UploadTask } from '@app/shared/types/database.types'

/**
 * 上传任务管理 Hook
 */
export function useUploadTasks() {
  const queryClient = useQueryClient()
  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()

  // 获取用户上传任务
  const { data: tasks = [], isLoading, refetch } = useQuery({
    queryKey: ['uploadTasks', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return []
      return window.uploadTask.getUserTasks({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 8000 // 减少轮询频率：每8秒刷新一次
  })

  // 获取上传统计
  const { data: stats } = useQuery({
    queryKey: ['uploadTaskStats', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return null
      return window.uploadTask.getTaskStats({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 15000 // 减少轮询频率：每15秒刷新一次
  })

  // 获取队列状态
  const { data: queueStatus } = useQuery({
    queryKey: ['uploadQueueStatus'],
    queryFn: () => window.uploadTask.getQueueStatus(),
    refetchInterval: 5000 // 减少轮询频率：每5秒刷新一次
  })

  // 创建上传任务
  const createTaskMutation = useMutation({
    mutationFn: async (params: any) => {
      return window.uploadTask.create(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 开始上传
  const startUploadMutation = useMutation({
    mutationFn: async (objectId: string) => {
      return window.uploadTask.startUpload({ objectId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 暂停上传
  const pauseUploadMutation = useMutation({
    mutationFn: async (objectId: string) => {
      console.log(`[useUploadTasks] 开始暂停任务 ${objectId}`)
      const result = await window.uploadTask.pauseUpload({ objectId })
      console.log(`[useUploadTasks] 暂停任务 ${objectId} 完成`)
      return result
    },
    onSuccess: () => {
      // 成功后立即刷新数据，确保状态同步
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    },
    onError: err => {
      console.error('[useUploadTasks] 暂停上传失败:', err)
    }
  })

  // 恢复上传
  const resumeUploadMutation = useMutation({
    mutationFn: async (objectId: string) => {
      console.log(`[useUploadTasks] 开始恢复任务 ${objectId}`)
      const result = await window.uploadTask.resumeUpload({ objectId })
      console.log(`[useUploadTasks] 恢复任务 ${objectId} 完成`)
      return result
    },
    onSuccess: () => {
      // 成功后立即刷新数据，确保状态同步
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    },
    onError: err => {
      console.error('[useUploadTasks] 恢复上传失败:', err)
    }
  })

  // 取消上传
  const cancelUploadMutation = useMutation({
    mutationFn: async (objectId: string) => {
      return window.uploadTask.cancelUpload({ objectId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 重试上传
  const retryUploadMutation = useMutation({
    mutationFn: async (objectId: string) => {
      return window.uploadTask.retryUpload({ objectId })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 批量操作
  const batchOperationMutation = useMutation({
    mutationFn: async (params: UploadTask.BatchParams) => {
      return window.uploadTask.batchOperation(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }
  })

  // 删除任务
  const deleteTaskMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.uploadTask.delete(taskId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 清理已完成任务
  const cleanupCompletedMutation = useMutation({
    mutationFn: async (olderThanDays?: number) => {
      if (!currentUser) return 0
      return window.uploadTask.cleanupCompleted({
        uid: String(currentUser),
        teamId: currentTeam,
        olderThanDays
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 上传文件内容
  const uploadFileContentMutation = useMutation({
    mutationFn: async (params: {
      objectId: string
      fileContent: ArrayBuffer
    }) => {
      return window.uploadTask.uploadFileContent(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 从路径上传文件
  const uploadFromPathMutation = useMutation({
    mutationFn: async (params: {
      objectId: string
      filePath: string
    }) => {
      return window.uploadTask.uploadFromPath(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['uploadTasks'] })
      queryClient.invalidateQueries({ queryKey: ['uploadTaskStats'] })
    }
  })

  // 监听上传进度事件
  useEffect(() => {
    const handleBatchProgressEvent = (_event: any, progressEvents: UploadTask.ProgressEvent[]) => {
      console.log(`[useUploadTasks] 接收到批量进度事件: ${progressEvents.length} 个任务`)

      // 批量更新任务进度
      queryClient.setQueryData(['uploadTasks', currentUser, currentTeam], (oldTasks: UploadTask.IUploadTask[] | undefined) => {
        if (!oldTasks) return oldTasks

        // 创建进度更新映射
        const progressMap = new Map<number, number>()
        progressEvents.forEach(event => {
          progressMap.set(event.task_id, event.progress)
          console.log(`[useUploadTasks] 任务${event.task_id}, 进度${(event.progress * 100).toFixed(1)}%`)
        })

        return oldTasks.map(task => {
          const newProgress = progressMap.get(task.id)
          if (newProgress !== undefined) {
            // 如果收到进度更新，说明任务正在上传中
            // 确保状态也同步更新为 UPLOADING
            const shouldUpdateStatus = task.status === UploadTask.Status.PENDING && newProgress > 0

            if (shouldUpdateStatus) {
              console.log(`[useUploadTasks] 任务${task.id} 状态从 PENDING 更新为 UPLOADING (进度: ${(newProgress * 100).toFixed(1)}%)`)
            }

            return {
              ...task,
              progress: newProgress,
              status: shouldUpdateStatus ? UploadTask.Status.UPLOADING : task.status,
              updated_at: Date.now()
            }
          }
          return task
        })
      })

      // 进度更新后，也刷新队列状态
      queryClient.invalidateQueries({ queryKey: ['uploadQueueStatus'] })
    }

    // 监听批量进度事件
    window.electronAPI?.ipcRenderer.on('batch-upload-progress', handleBatchProgressEvent)

    return () => {
      window.electronAPI?.ipcRenderer.removeListener('batch-upload-progress', handleBatchProgressEvent)
    }
  }, [currentUser, currentTeam, queryClient])

  // 便捷方法
  const getTasksByStatus = useCallback((status: UploadTask.Status) => {
    return tasks.filter(task => task.status === status)
  }, [tasks])

  const getPendingTasks = useCallback(() => getTasksByStatus(UploadTask.Status.PENDING), [getTasksByStatus])
  const getUploadingTasks = useCallback(() => getTasksByStatus(UploadTask.Status.UPLOADING), [getTasksByStatus])
  const getPausedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.PAUSED), [getTasksByStatus])
  const getCompletedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.COMPLETED), [getTasksByStatus])
  const getFailedTasks = useCallback(() => getTasksByStatus(UploadTask.Status.FAILED), [getTasksByStatus])

  return {
    // 数据
    tasks,
    stats,
    queueStatus,
    isLoading,

    // 查询方法
    refetch,
    getTasksByStatus,
    getPendingTasks,
    getUploadingTasks,
    getPausedTasks,
    getCompletedTasks,
    getFailedTasks,

    // 操作方法
    createTask: createTaskMutation.mutateAsync,
    startUpload: startUploadMutation.mutateAsync,
    pauseUpload: pauseUploadMutation.mutateAsync,
    resumeUpload: resumeUploadMutation.mutateAsync,
    cancelUpload: cancelUploadMutation.mutateAsync,
    retryUpload: retryUploadMutation.mutateAsync,
    batchOperation: batchOperationMutation.mutateAsync,
    deleteTask: deleteTaskMutation.mutateAsync,
    cleanupCompleted: cleanupCompletedMutation.mutateAsync,
    uploadFileContent: uploadFileContentMutation.mutateAsync,
    uploadFromPath: uploadFromPathMutation.mutateAsync,

    // 加载状态
    isCreating: createTaskMutation.isPending,
    isOperating: startUploadMutation.isPending ||
                 pauseUploadMutation.isPending ||
                 resumeUploadMutation.isPending ||
                 cancelUploadMutation.isPending ||
                 retryUploadMutation.isPending,
    isBatchOperating: batchOperationMutation.isPending,
    isDeleting: deleteTaskMutation.isPending,
    isCleaning: cleanupCompletedMutation.isPending,
    isUploading: uploadFileContentMutation.isPending || uploadFromPathMutation.isPending
  }
}

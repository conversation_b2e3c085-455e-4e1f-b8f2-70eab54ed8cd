import React, { useState, useCallback } from 'react'
import { useUploadTasks } from '@/hooks/useUploadTasks'
import { UploadTask } from '@app/shared/types/database.types'
import { Button } from '@/components/ui/button'

import { Badge } from '@/components/ui/badge'
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  Trash2,
  Upload,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Loader2
} from 'lucide-react'

/**
 * 上传任务管理器组件
 */
export const UploadTaskManager: React.FC = () => {
  // 跟踪每个任务的操作状态（使用 objectId）
  const [taskOperations, setTaskOperations] = useState<Record<string, string | null>>({})
  // 防抖：记录最后一次操作时间（使用 objectId）
  const [lastOperationTime, setLastOperationTime] = useState<Record<string, number>>({})

  const {
    tasks,
    stats,
    queueStatus,
    isLoading,
    startUpload,
    pauseUpload,
    resumeUpload,
    cancelUpload,
    retryUpload,
    deleteTask,
    cleanupCompleted
  } = useUploadTasks()

  // 获取状态图标
  const getStatusIcon = (status: UploadTask.Status) => {
    switch (status) {
      case UploadTask.Status.PENDING:
        return <Clock className="w-4 h-4 text-gray-500" />
      case UploadTask.Status.UPLOADING:
        return <Upload className="w-4 h-4 text-blue-500 animate-pulse" />
      case UploadTask.Status.PAUSED:
        return <Pause className="w-4 h-4 text-yellow-500" />
      case UploadTask.Status.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case UploadTask.Status.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />
      case UploadTask.Status.CANCELLED:
        return <Square className="w-4 h-4 text-gray-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />
    }
  }

  // 获取状态文本
  const getStatusText = (status: UploadTask.Status) => {
    const statusMap = {
      [UploadTask.Status.PENDING]: '等待上传',
      [UploadTask.Status.UPLOADING]: '上传中',
      [UploadTask.Status.PAUSED]: '已暂停',
      [UploadTask.Status.COMPLETED]: '上传完成',
      [UploadTask.Status.FAILED]: '上传失败',
      [UploadTask.Status.CANCELLED]: '已取消'
    }
    return statusMap[status] || '未知状态'
  }

  // 获取状态颜色
  const getStatusColor = (status: UploadTask.Status) => {
    switch (status) {
      case UploadTask.Status.PENDING:
        return 'secondary'
      case UploadTask.Status.UPLOADING:
        return 'default'
      case UploadTask.Status.PAUSED:
        return 'outline'
      case UploadTask.Status.COMPLETED:
        return 'default'
      case UploadTask.Status.FAILED:
        return 'destructive'
      case UploadTask.Status.CANCELLED:
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  // 处理任务操作
  const handleTaskAction = useCallback(async (task: any, action: string) => {
    const objectId = task.object_id
    if (!objectId) {
      console.error(`任务 ${task.id} 缺少 object_id，无法执行操作`)
      return
    }

    const now = Date.now()
    const lastTime = lastOperationTime[objectId] || 0

    // 减少防抖时间：200ms 内不允许重复操作（避免误触）
    if (now - lastTime < 200) {
      console.log(`任务 ${objectId} 操作过于频繁，忽略重复点击`)
      return
    }

    // 检查是否已经在执行操作
    if (taskOperations[objectId]) {
      console.log(`任务 ${objectId} 正在执行 ${taskOperations[objectId]} 操作，忽略重复点击`)
      return
    }

    try {
      // 记录操作时间
      setLastOperationTime(prev => ({ ...prev, [objectId]: now }))
      // 设置操作状态
      setTaskOperations(prev => ({ ...prev, [objectId]: action }))

      console.log(`[UploadTaskManager] 执行任务 ${objectId} 的 ${action} 操作`)

      switch (action) {
        case 'start':
          await startUpload(objectId)
          break
        case 'pause':
          await pauseUpload(objectId)
          break
        case 'resume':
          await resumeUpload(objectId)
          break
        case 'cancel':
          await cancelUpload(objectId)
          break
        case 'retry':
          await retryUpload(objectId)
          break
        case 'delete':
          await deleteTask(task.id) // 删除操作仍使用 id
          break
      }

      console.log(`[UploadTaskManager] 任务 ${objectId} 的 ${action} 操作完成`)
    } catch (error) {
      console.error(`[UploadTaskManager] 任务 ${objectId} 的 ${action} 操作失败:`, error)
    } finally {
      // 清除操作状态
      setTaskOperations(prev => {
        const newState = { ...prev }
        delete newState[objectId]
        return newState
      })
    }
  }, [taskOperations, lastOperationTime, startUpload, pauseUpload, resumeUpload, cancelUpload, retryUpload, deleteTask])

  if (isLoading) {
    return <div className="p-4">加载中...</div>
  }

  return (
    <div className="p-4 space-y-4">
      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-sm text-blue-600">总任务数</div>
            <div className="text-2xl font-bold text-blue-700">{stats.total_count}</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-sm text-green-600">已完成</div>
            <div className="text-2xl font-bold text-green-700">{stats.completed_count}</div>
          </div>
          <div className="bg-yellow-50 p-3 rounded-lg">
            <div className="text-sm text-yellow-600">上传中</div>
            <div className="text-2xl font-bold text-yellow-700">{stats.uploading_count}</div>
          </div>
          <div className="bg-red-50 p-3 rounded-lg">
            <div className="text-sm text-red-600">失败</div>
            <div className="text-2xl font-bold text-red-700">{stats.failed_count}</div>
          </div>
        </div>
      )}

      {/* 队列状态 */}
      {queueStatus && (
        <div className=" p-3 rounded-lg">
          <div className="text-sm text-gray-600">
            队列状态: {queueStatus.active_count}/{queueStatus.max_concurrent} 活跃, 
            {queueStatus.pending_count} 等待, {queueStatus.paused_count} 暂停
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => cleanupCompleted(7)}
        >
          清理已完成 (7天前)
        </Button>
      </div>

      {/* 任务列表 */}
      <div className="space-y-2">
        {tasks.map(task => (
          <div key={task.id} className="border rounded-lg p-4 ">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 flex-1">
                {getStatusIcon(task.status)}
                {task.id}
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{task.name}</div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusColor(task.status) as any}>
                    {getStatusText(task.status)}
                  </Badge>
                  {taskOperations[task.object_id] && (
                    <Badge variant="secondary" className="text-xs">
                      {taskOperations[task.object_id]}中...
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                {/* 操作按钮 */}
                {task.status === UploadTask.Status.PENDING && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTaskAction(task, 'start')}
                    disabled={!!taskOperations[task.object_id]}
                  >
                    {taskOperations[task.object_id] === 'start' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                  </Button>
                )}

                {(task.status === UploadTask.Status.UPLOADING ||
                  (task.status === UploadTask.Status.PENDING && task.progress > 0)) && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTaskAction(task, 'pause')}
                    disabled={!!taskOperations[task.object_id]}
                  >
                    {taskOperations[task.object_id] === 'pause' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Pause className="w-4 h-4" />
                    )}
                  </Button>
                )}

                {task.status === UploadTask.Status.PAUSED && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTaskAction(task, 'resume')}
                    disabled={!!taskOperations[task.object_id]}
                  >
                    {taskOperations[task.object_id] === 'resume' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                  </Button>
                )}

                {task.status === UploadTask.Status.FAILED && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTaskAction(task, 'retry')}
                    disabled={!!taskOperations[task.object_id]}
                  >
                    {taskOperations[task.object_id] === 'retry' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <RotateCcw className="w-4 h-4" />
                    )}
                  </Button>
                )}

                {(task.status === UploadTask.Status.PENDING ||
                  task.status === UploadTask.Status.UPLOADING ||
                  task.status === UploadTask.Status.PAUSED) && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleTaskAction(task, 'cancel')}
                    disabled={!!taskOperations[task.object_id]}
                  >
                    {taskOperations[task.object_id] === 'cancel' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Square className="w-4 h-4" />
                    )}
                  </Button>
                )}

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleTaskAction(task, 'delete')}
                  disabled={!!taskOperations[task.object_id]}
                >
                  {taskOperations[task.object_id] === 'delete' ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
            
            {/* 进度条 */}
            <div className="text-xs mt-1">
              {(task.progress * 100).toFixed(1)}% - {task.reason && `错误: ${task.reason}`}
            </div>
            
            {/* 错误信息 */}
            {task.status === UploadTask.Status.FAILED && task.reason && (
              <div className="mt-2 text-sm text-red-600  p-2 rounded">
                错误: {task.reason}
              </div>
            )}
          </div>
        ))}
        
        {tasks.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            暂无上传任务
          </div>
        )}
      </div>
    </div>
  )
}

import { Injectable, Inject, forwardRef } from '@nestjs/common'
import { dialog } from 'electron'
import { statSync } from 'fs'
import { basename } from 'path'
import { UploadTaskModel } from '@/infra/models/UploadTaskModel.js'
import { UploadTaskRepository } from './upload-task.repository.js'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'

import { createHash } from 'crypto'
import { PaginatedResult, PaginationParams, UploadTask } from '@app/shared/types/database.types.js'
import  { UploadQueueManager } from './upload-queue-manager.js'
import { UploadTaskIPCClient, UploadTaskIPCClientUniqueUtilities } from '@app/shared/types/ipc/upload-task.js'
import { IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'

/**
 * 上传任务服务类
 */
@Injectable()
export class UploadTaskService extends CrudableBaseService<
  UploadTaskModel,
  UploadTask.CreateParams,
  UploadTask.UpdateParams, 
  UploadTask.QueryParams,
  UploadTaskRepository
>  implements UploadTaskIPCClientUniqueUtilities {

  /**
   * 默认队列配置
   */
  private queueConfig: UploadTask.QueueConfig = {
    max_concurrent_uploads: 3, 
    retry_attempts: 3,
    retry_delay: 5000,
    chunk_size: 1024 * 1024 // 1MB
  }

  constructor(
    @Inject(UploadTaskRepository)  repository: UploadTaskRepository,
    @Inject(forwardRef(() => UploadQueueManager)) private queueManager: UploadQueueManager
  ) {
    super(repository)
    this.scheduleInitialization()
  }
  startUpload(data: { objectId: string }): boolean {
    if (!data) {
      throw new IPCHandlerError('开始上传任务参数不能为空')
    }
    if (!data.objectId) {
      throw new IPCHandlerError('任务objectId不能为空')
    }
    return this.updateTaskStatusByObjectId(data.objectId, UploadTask.Status.UPLOADING)
  }

  async pauseUpload(data: { objectId: string }): Promise<boolean> {
    if (!data) {
      throw new IPCHandlerError('暂停上传任务参数不能为空')
    }
    if (!data.objectId) {
      throw new IPCHandlerError('任务objectId不能为空')
    }

    const { objectId } = data
    const task = this.repository.findByObjectId(objectId)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canPause()) {
      throw new Error('任务当前状态不允许暂停')
    }

    // 调用队列管理器的暂停方法，它会处理正在上传的任务或直接更新状态
    if (this.queueManager) {
      console.log(`[UploadTaskService] 调用队列管理器暂停任务 ${objectId}`)
      return await this.queueManager.pauseUploadByObjectId(objectId)
    }

    // 如果没有队列管理器，直接更新数据库状态
    return this.updateTaskStatusByObjectId(objectId, UploadTask.Status.PAUSED)
  }
  async resumeUpload(data: { objectId: string }): Promise<boolean> {
    if (!data) {
      throw new IPCHandlerError('恢复上传任务参数不能为空')
    }
    if (!data.objectId) {
      throw new IPCHandlerError('任务objectId不能为空')
    }

    const { objectId } = data
    const task = this.repository.findByObjectId(objectId)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canResume()) {
      throw new Error('任务当前状态不允许恢复')
    }

    // 调用队列管理器的恢复方法，它会处理暂停的任务或直接更新状态
    if (this.queueManager) {
      console.log(`[UploadTaskService] 调用队列管理器恢复任务 ${objectId}`)
      return await this.queueManager.resumeUploadByObjectId(objectId)
    }

    // 如果没有队列管理器，直接更新状态为等待
    return this.updateTaskStatusByObjectId(objectId, UploadTask.Status.PENDING)
  }
  cancelUpload(data: { objectId: string }): boolean  {
    if (!data) {
      throw new IPCHandlerError('取消上传任务参数不能为空')
    }
    if (!data.objectId) {
      throw new IPCHandlerError('任务objectId不能为空')
    }

    const { objectId } = data
    const task = this.repository.findByObjectId(objectId)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canCancel()) {
      throw new Error('任务当前状态不允许取消')
    }

    return this.updateTaskStatusByObjectId(objectId, UploadTask.Status.CANCELLED, '用户取消')
  }

  retryUpload(data: { objectId: string }): boolean {
    if (!data) {
      throw new IPCHandlerError('重试上传任务参数不能为空')
    }
    if (!data.objectId) {
      throw new IPCHandlerError('任务objectId不能为空')
    }

    const { objectId } = data
    const task = this.repository.findByObjectId(objectId)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (!task.canRetry()) {
      throw new Error('任务当前状态不允许重试')
    }

    task.reset()
    return this.repository.updateByObjectId(objectId, {
      progress: task.progress,
      status: task.status,
      reason: task.reason
    })
  }
  batchOperation(data: UploadTask.BatchParams): number {
    if (!data) {
      throw new IPCHandlerError('批量操作参数不能为空')
    }
    if (!data.objectIds || !Array.isArray(data.objectIds) || data.objectIds.length === 0) {
      throw new IPCHandlerError('任务objectId数组不能为空')
    }
    if (!data.action) {
      throw new IPCHandlerError('操作类型不能为空')
    }

    switch (data.action) {
      case 'pause': {
        return this.batchUpdateStatusByObjectId(data.objectIds, UploadTask.Status.PAUSED)
      }
      case 'resume': {
        return this.batchUpdateStatusByObjectId(data.objectIds, UploadTask.Status.PENDING)
      }
      case 'cancel': {
        return this.batchUpdateStatusByObjectId(data.objectIds, UploadTask.Status.CANCELLED, '用户取消')
      }
      case 'retry': {
        let retryCount = 0
        for (const objectId of data.objectIds) {
          try {
            if (this.retryUpload({ objectId })) {
              retryCount++
            }
          } catch (error) {
            // 忽略单个任务重试失败
          }
        }
        return retryCount
      }
      case 'delete': {
        return this.batchDeleteByObjectId(data.objectIds)
      }
      default: {
        throw new IPCHandlerError(`不支持的操作类型: ${data.action}`)
      }
    }
  }
  
  /**
   * 设置队列管理器引用（由队列管理器调用）
   */
  setQueueManager(queueManager: UploadQueueManager): void {
    this.queueManager = queueManager
  }

  /**
   * 调度初始化，等待数据库准备就绪
   */
  private scheduleInitialization(): void {
    const checkAndInit = () => {
      try {
        // 尝试访问数据库，如果失败则继续等待
        this.repository.resetUploadingTasks()
        console.log('[UploadTaskService] 初始化成功')
      } catch (error) {
        // 数据库还未准备好，继续等待
        setTimeout(checkAndInit, 2000)
      }
    }

    // 延迟开始检查
    setTimeout(checkAndInit, 3000)
  }

  /**
   * 获取用户上传任务
   */
  getUserTasks(data: { uid: string, status?: UploadTask.Status, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('获取用户上传任务参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }
        
    try {
      const { uid, status, teamId } = data
      return this.repository.findUserTasks(uid, status, teamId)
    } catch (error: any) {
      throw new Error(`获取用户上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取文件夹下的上传任务
   */
  getTasksByFolder(data: { folderId: string, uid: string, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('获取文件夹上传任务参数不能为空')
    }
    if (!data.folderId) {
      throw new IPCHandlerError('文件夹ID不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }
    try {
      const { folderId, uid, teamId } = data
      return this.repository.findTasksByFolder(folderId, uid, teamId)
    } catch (error: any) {
      throw new Error(`获取文件夹上传任务失败: ${error.message}`)
    }
  }

  /**
   * 根据状态获取任务
   */
  getTasksByStatus(status: UploadTask.Status, uid?: string, teamId?: number | null): UploadTaskModel[] {
    try {
      return this.repository.findTasksByStatus(status, uid, teamId)
    } catch (error: any) {
      throw new Error(`根据状态获取上传任务失败: ${error.message}`)
    }
  }

  /**
   * 搜索上传任务
   */
  searchTasks(data: { keyword: string, uid: string, teamId?: number | null }): UploadTaskModel[] {
    if (!data) {
      throw new IPCHandlerError('搜索上传任务参数不能为空')
    }
    if (!data.keyword) {
      throw new IPCHandlerError('搜索关键词不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try { 
      const { keyword, uid, teamId } = data
      return this.repository.search(keyword, uid, teamId)
    } catch (error: any) {
      throw new Error(`搜索上传任务失败: ${error.message}`)
    }
  }

  /**
   * 获取任务统计
   */
  getTaskStats(data: { uid: string, teamId?: number | null }): UploadTask.StatsResult {
    if (!data) {
      throw new IPCHandlerError('获取上传任务统计参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }
        
    try {
      const { uid, teamId } = data
      return this.repository.getTaskStats(uid, teamId)
    } catch (error: any) {
      throw new Error(`获取上传任务统计失败: ${error.message}`)
    }
  }

  /**
   * 更新任务进度
   */
  updateTaskProgress(id: number, progress: number): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new Error('任务不存在')
      }

      task.updateProgress(progress)
      return this.repository.update(id, {
        progress: task.progress
      })
    } catch (error: any) {
      throw new Error(`更新任务进度失败: ${error.message}`)
    }
  }

  /**
   * 更新任务状态
   */
  updateTaskStatus(id: number, status: UploadTask.Status, reason?: string): boolean {
    try {
      const task = this.repository.findById(id)
      if (!task) {
        throw new Error('任务不存在')
      }

      task.setStatus(status, reason)
      return this.repository.update(id, {
        status: task.status,
        reason: task.reason
      })
    } catch (error: any) {
      throw new Error(`更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 根据 objectId 更新任务状态
   */
  updateTaskStatusByObjectId(objectId: string, status: UploadTask.Status, reason?: string): boolean {
    try {
      const task = this.repository.findByObjectId(objectId)
      if (!task) {
        throw new Error('任务不存在')
      }

      task.setStatus(status, reason)
      return this.repository.updateByObjectId(objectId, {
        status: task.status,
        reason: task.reason
      })
    } catch (error: any) {
      throw new Error(`根据objectId更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 根据 objectId 查找任务
   */
  findByObjectId(objectId: string, includeDeleted = false): UploadTaskModel | null {
    try {
      return this.repository.findByObjectId(objectId, includeDeleted)
    } catch (error: any) {
      throw new Error(`根据objectId查找任务失败: ${error.message}`)
    }
  }

  /**
   * 批量更新任务状态
   */
  batchUpdateStatus(ids: number[], status: UploadTask.Status, reason?: string): number {
    try {
      return this.repository.batchUpdateStatus(ids, status, reason)
    } catch (error: any) {
      throw new Error(`批量更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 根据 objectId 批量更新任务状态
   */
  batchUpdateStatusByObjectId(objectIds: string[], status: UploadTask.Status, reason?: string): number {
    try {
      return this.repository.batchUpdateStatusByObjectId(objectIds, status, reason)
    } catch (error: any) {
      throw new Error(`根据objectId批量更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 根据 objectId 批量删除任务
   */
  batchDeleteByObjectId(objectIds: string[]): number {
    try {
      return this.repository.batchSoftDeleteByObjectId(objectIds)
    } catch (error: any) {
      throw new Error(`根据objectId批量删除任务失败: ${error.message}`)
    }
  }

  /**
   * 获取队列配置
   */
  getQueueConfig(): UploadTask.QueueConfig {
    return { ...this.queueConfig }
  }

  /**
   * 更新队列配置
   */
  updateQueueConfig(config: Partial<UploadTask.QueueConfig>): boolean {
    if (!config) {
      throw new IPCHandlerError('队列配置不能为空')
    }
    try {
      this.queueConfig = { ...this.queueConfig, ...config }
      return true
    } catch (error: any) {
      throw new Error(`更新队列配置失败: ${error.message}`)
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    active_count: number
    pending_count: number
    paused_count: number
    max_concurrent: number
  } {
    try {
      const uploadingTasks = this.repository.findTasksByStatus(UploadTask.Status.UPLOADING)
      const pendingTasks = this.repository.findTasksByStatus(UploadTask.Status.PENDING)
      const pausedTasks = this.repository.findTasksByStatus(UploadTask.Status.PAUSED)

      return {
        active_count: uploadingTasks.length,
        pending_count: pendingTasks.length,
        paused_count: pausedTasks.length,
        max_concurrent: this.queueConfig.max_concurrent_uploads
      }
    } catch (error: any) {
      throw new Error(`获取队列状态失败: ${error.message}`)
    }
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompleted(data: { uid: string, teamId?: number | null, olderThanDays?: number }): number {
    if (!data) {
      throw new IPCHandlerError('清理已完成任务参数不能为空')
    }
    if (!data.uid) {
      throw new IPCHandlerError('用户ID不能为空')
    }

    try {
      const { uid, teamId, olderThanDays = 30 } = data
      return this.repository.cleanupCompleted(uid, teamId, olderThanDays)
    } catch (error: any) {
      throw new Error(`清理已完成任务失败: ${error.message}`)
    }
  }

  /**
   * 上传文件内容
   */
  async uploadFileContent(data: {
    objectId: string,
    fileContent: ArrayBuffer | Uint8Array,
  }
  ): Promise<{ success: boolean, url?: string, error?: string }> {
    if (!data) {
      throw new IPCHandlerError('上传文件内容参数不能为空')
    }
    if (!data.objectId) {
      throw new IPCHandlerError('任务objectId不能为空')
    }
    if (!data.fileContent) {
      throw new IPCHandlerError('文件内容不能为空')
    }

    const { objectId, fileContent } = data

    try {
      // 获取任务信息
      const task = this.repository.findByObjectId(objectId)
      if (!task) {
        return { success: false, error: '任务不存在' }
      }

      // 检查任务状态
      if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
        return { success: false, error: '任务当前状态不允许上传' }
      }

      // 更新任务状态为上传中
      this.updateTaskStatusByObjectId(objectId, UploadTask.Status.UPLOADING)

      // 转换文件内容为 Buffer
      const buffer = fileContent instanceof ArrayBuffer
        ? Buffer.from(fileContent)
        : Buffer.from(fileContent.buffer)

      // 计算文件哈希
      const hash = createHash('md5').update(buffer).digest('hex')

      // 更新任务哈希值
      this.repository.updateByObjectId(objectId, { hash })

      // 将任务状态设置为等待，让队列管理器处理
      this.updateTaskStatusByObjectId(objectId, UploadTask.Status.PENDING)

      console.log(`[UploadTaskService] 任务 ${objectId} 已添加到上传队列`)
      return { success: true }
    } catch (error: any) {
      console.error('[UploadTaskService] 上传文件内容失败:', error)
      this.updateTaskStatusByObjectId(objectId, UploadTask.Status.FAILED, error.message || '上传异常')
      return { success: false, error: error.message || '上传异常' }
    }
  }

  /**
   * 选择文件
   */
  async selectFiles(
    data: {
      multiple?: boolean,
      filters?: Array<{ name: string, extensions: string[] }>,
      folder: boolean,
    }
  ): Promise<string[]> {
    try {
      const { multiple = false, filters, folder = false } = data
      const defaultFilters = [
        { name: '视频文件', extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v'] },
        { name: '音频文件', extensions: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'] },
        { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'] },
        { name: '所有文件', extensions: ['*'] }
      ]

      const properties: Electron.OpenDialogOptions['properties'] = []

      if (folder) {
        properties.push('openDirectory')
      } else {
        properties.push('openFile')
      }
  
      if (multiple) {
        properties.push('multiSelections')
      }
  
      const result = await dialog.showOpenDialog({
        properties,
        filters: folder ? undefined : (filters || defaultFilters) 
      })
  
      return result.canceled ? [] : result.filePaths
    } catch (error: any) {
      console.error('[UploadTaskService] 选择文件失败:', error)
      return []
    }
  }

  /**
   * 从本地路径上传文件
   */
  async uploadFromPath(
    data: {
      objectId: string,
      filePath: string
    }
  ): Promise<{ success: boolean, url?: string, error?: string }> {
    if (!data) {
      throw new IPCHandlerError('从路径上传文件参数不能为空')
    }
    if (!data.objectId) {
      throw new IPCHandlerError('任务objectId不能为空')
    }
    if (!data.filePath) {
      throw new IPCHandlerError('文件路径不能为空')
    }

    try {
      const { objectId, filePath } = data
      // 获取任务信息
      const task = this.repository.findByObjectId(objectId)
      if (!task) {
        return { success: false, error: '任务不存在' }
      }

      // 检查任务状态
      if (!task.isPending() && !task.isPaused() && !task.isFailed()) {
        return { success: false, error: '任务当前状态不允许上传' }
      }

      // 检查文件是否存在
      try {
        const stats = statSync(filePath)
        if (!stats.isFile()) {
          return { success: false, error: '指定路径不是文件' }
        }
      } catch {
        return { success: false, error: '文件不存在或无法访问' }
      }

      // 更新任务的本地路径
      this.repository.updateByObjectId(objectId, {
        local_path: filePath,
        name: basename(filePath)
      })

      // 将任务状态设置为等待，让队列管理器处理
      this.updateTaskStatusByObjectId(objectId, UploadTask.Status.PENDING)

      console.log(`[UploadTaskService] 任务 ${objectId} 已添加到上传队列，文件路径: ${filePath}`)
      return { success: true }
    } catch (error: any) {
      console.error('[UploadTaskService] 从路径上传文件失败:', error)
      this.updateTaskStatusByObjectId(data.objectId, UploadTask.Status.FAILED, error.message || '上传异常')
      return { success: false, error: error.message || '上传异常' }
    }
  }
}
